const { FlatCompat } = require('@eslint/eslintrc')
const baseConfig = require('../../../eslint.config.js')
const js = require('@eslint/js')

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended
})

module.exports = [
  ...baseConfig,
  ...compat
    .config({ extends: ['plugin:@nx/angular', 'plugin:@angular-eslint/template/process-inline-templates'] })
    .map((config) => ({
      ...config,
      files: ['**/*.ts'],
      rules: {
        '@angular-eslint/directive-selector': [
          'error',
          {
            type: 'attribute',
            prefix: 'ft',
            style: 'camelCase'
          }
        ],
        '@angular-eslint/component-selector': [
          'error',
          {
            type: 'element',
            prefix: 'ft',
            style: 'kebab-case'
          }
        ]
      }
    })),
  ...compat.config({ extends: ['plugin:@nx/angular-template'] }).map((config) => ({
    ...config,
    files: ['**/*.html'],
    rules: {}
  })),
  ...compat.config({ extends: ['plugin:json-schema-validator/recommended'] }).map((config) => ({
    ...config,
    rules: {
      'json-schema-validator/no-invalid': [
        'error',
        {
          schemas: [
            {
              fileMatch: ['**/environment*.json'],
              schema: 'libs/billing-back-office/environment/src/lib/schema.json'
            }
          ]
        }
      ]
    }
  })),
  {
    files: ['**/*.ts'],
    rules: {
      '@angular-eslint/prefer-standalone': 'off'
    }
  },
  {
    files: ['**/*.yaml'],
    rules: {
      'json-schema-validator/no-invalid': 'off'
    }
  }
]
