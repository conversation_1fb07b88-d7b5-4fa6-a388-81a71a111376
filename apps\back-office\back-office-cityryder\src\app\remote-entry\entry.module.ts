import { EcomuiSnackBarService } from '@ft/ecom-ui2/snackbar'
import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { RouterModule } from '@angular/router'
import { CityryderDataAccessCourierModule } from '@ft/cityryder-data-access-courier'
import { CityryderDataAccessOrderModule } from '@ft/cityryder-data-access-order'
import { CityryderDataAccessBranchModule } from '@ft/cityryder-back-office-data-access-branch'
import { environment } from '../../environments/environment'
import { HTTP_INTERCEPTORS } from '@angular/common/http'
import { AuthInterceptor } from 'angular-auth-oidc-client'
import { AccesForbiddenInterceptor, ERROR_MESSAGE_SERVICE } from '@ft/shared-utils'
import { CITYRYDER_BACK_OFFICE_ENV } from '@ft/cityryder-back-office-environment'
import { CityryderDataAccessBackOfficeModule } from '@ft/cityryder-back-office-data-access-back-office'
import { CityryderDataAccessDashboardModule } from '@ft/cityryder-back-office-data-access-dashboard'
import { CityryderDataAccessRouteModule } from '@ft/cityryder-back-office-data-access-route'
import { CityryderDataAccessIncidentModule } from '@ft/cityryder-back-office-data-access-incident'

@NgModule({
  imports: [
    CommonModule,
    CityryderDataAccessCourierModule.forRoot({ rootUrl: environment.apiUrl }),
    CityryderDataAccessOrderModule.forRoot({ rootUrl: environment.apiUrl }),
    CityryderDataAccessBranchModule.forRoot({ rootUrl: environment.apiUrl }),
    CityryderDataAccessBackOfficeModule.forRoot({ rootUrl: environment.apiUrl }),
    CityryderDataAccessDashboardModule.forRoot({ rootUrl: environment.apiUrl }),
    CityryderDataAccessRouteModule.forRoot({ rootUrl: environment.apiUrl }),
    CityryderDataAccessIncidentModule.forRoot({ rootUrl: environment.apiUrl }),
    RouterModule.forChild([
      {
        path: '',
        loadChildren: () => import('@ft/cityryder-shell').then((m) => m.CityryderShellModule)
      }
    ])
  ],
  providers: [
    { provide: ERROR_MESSAGE_SERVICE, useClass: EcomuiSnackBarService },
    { provide: CITYRYDER_BACK_OFFICE_ENV, useValue: environment },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: AccesForbiddenInterceptor, multi: true }
  ]
})
export class RemoteEntryModule {}
