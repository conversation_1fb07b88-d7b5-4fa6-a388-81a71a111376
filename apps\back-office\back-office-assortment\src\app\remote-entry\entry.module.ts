import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { RouterModule } from '@angular/router'
import { environment } from '../../environments/environment'
import { AssortmentDataAccessModule } from '@ft/data-access-assortment'

@NgModule({
  imports: [
    CommonModule,
    AssortmentDataAccessModule.forRoot({ rootUrl: environment.apiUrl }),
    RouterModule.forChild([
      {
        path: '',
        loadChildren: () => import('@ft/assortment-shell').then((m) => m.AssortmentShellModule)
      }
    ])
  ],
  providers: []
})
export class RemoteEntryModule {}
