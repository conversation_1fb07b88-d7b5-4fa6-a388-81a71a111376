billing_qualityspy:
  extends: .qualityspy-v2
  needs: []
  variables:
    QS_CONFIG_PATH: apps/back-office/back-office-billing/qualityspy.yaml
    QS_NAME: billing--core--back-office

.base_billing_deploy:
  needs:
    - assemble
  variables:
    PRODUCT_NAME: billing
    APP_NAME: billing-back-office
    CONTAINER: 'spa-billing-back-office'
    UNCACHED_FILES: 'remoteEntry.mjs *environment.json'

billing_feature:
  extends:
    - .base_billing_deploy
    - .deploy_feature
  variables:
    APP_DEPLOY_URL: 'https://d3pbdyzf7elowv.cloudfront.net/apps/billing-back-office/${CI_COMMIT_REF_SLUG}/'
  environment:
    name: 'back-office-feature/billing'

dev_billing:
  extends:
    - .base_billing_deploy
    - .deploy_dev
  variables:
    APP_DEPLOY_URL: 'https://d36zhdz6hdzebq.cloudfront.net/apps/billing-back-office/latest/'
  environment:
    name: 'back-office-dev/billing'

qa_billing:
  extends:
    - .base_billing_deploy
    - .deploy_qa
  variables:
    APP_DEPLOY_URL: 'https://d3pbdyzf7elowv.cloudfront.net/apps/billing-back-office/latest/'
  environment:
    name: 'back-office-qa/billing'

stage_billing:
  extends:
    - .base_billing_deploy
    - .deploy_stage
  variables:
    APP_DEPLOY_URL: 'https://d3kd5456tkyp80.cloudfront.net/apps/billing-back-office/latest/'
  environment:
    name: 'back-office-stage/billing'

prod_billing:
  extends:
    - .base_billing_deploy
    - .deploy_prod
  variables:
    APP_DEPLOY_URL: 'https://staticv2.silpo.ua/apps/billing-back-office/latest/'
  environment:
    name: 'back-office-prod/billing'
