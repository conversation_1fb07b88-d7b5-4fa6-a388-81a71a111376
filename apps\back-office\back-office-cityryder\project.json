{"name": "back-office-cityryder", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "ft", "sourceRoot": "apps/back-office/back-office-cityryder/src", "tags": ["scope:back-office:cityryder", "type:app", "sonar:back-office"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "dependsOn": [{"dependencies": true, "target": "build"}], "options": {"outputPath": "dist/apps/cityryder-back-office", "index": "apps/back-office/back-office-cityryder/src/index.html", "main": "apps/back-office/back-office-cityryder/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/back-office-cityryder/tsconfig.app.json", "assets": ["apps/back-office/back-office-cityryder/src/favicon.ico", "apps/back-office/back-office-cityryder/src/assets"], "styles": ["apps/back-office/back-office-cityryder/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/back-office/back-office-cityryder/webpack.config.js"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "apps/back-office/back-office-cityryder/src/environments/config.ts", "with": "apps/back-office/back-office-cityryder/src/environments/config.ci.ts"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/back-office/back-office-cityryder/webpack.prod.config.js"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4201, "publicHost": "http://localhost:4201"}, "configurations": {"production": {"buildTarget": "back-office-cityryder:build:production"}, "development": {"buildTarget": "back-office-cityryder:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "back-office-cityryder:build"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/back-office/back-office-cityryder/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "back-office-cityryder:build", "port": 4201, "watch": false}, "configurations": {"development": {"buildTarget": "back-office-cityryder:build:development"}, "production": {"buildTarget": "back-office-cityryder:build:production"}}}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "city<PERSON>der", "hostUrl": "https://sq.foodtech.team/", "projectKey": "cityryder--core--back-office", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "libs/shared/**, **/data-access/**, **/data-access-async-api/**"}}}}