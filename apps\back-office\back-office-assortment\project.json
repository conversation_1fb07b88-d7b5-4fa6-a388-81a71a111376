{"name": "back-office-assortment", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "ft", "sourceRoot": "apps/back-office/back-office-assortment/src", "tags": ["scope:back-office:assortment", "type:app", "sonar:back-office"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "dependsOn": [{"dependencies": true, "target": "build"}], "options": {"outputPath": "dist/apps/assortment-back-office", "index": "apps/back-office/back-office-assortment/src/index.html", "main": "apps/back-office/back-office-assortment/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/back-office-assortment/tsconfig.app.json", "assets": ["apps/back-office/back-office-assortment/src/favicon.ico", "apps/back-office/back-office-assortment/src/assets"], "styles": ["apps/back-office/back-office-assortment/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/back-office/back-office-assortment/webpack.config.js"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "apps/back-office/back-office-assortment/src/environments/config.ts", "with": "apps/back-office/back-office-assortment/src/environments/config.ci.ts"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/back-office/back-office-assortment/webpack.prod.config.js"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4217, "publicHost": "http://localhost:4217"}, "configurations": {"production": {"buildTarget": "back-office-assortment:build:production"}, "development": {"buildTarget": "back-office-assortment:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "back-office-assortment:build"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/back-office/back-office-assortment/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "back-office-assortment:build", "port": 4217, "watch": false}, "configurations": {"development": {"buildTarget": "back-office-assortment:build:development"}, "production": {"buildTarget": "back-office-assortment:build:production"}}}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "assortment", "hostUrl": "https://sq.foodtech.team/", "projectKey": "assortment--core--back-office", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "libs/shared/**, **/data-access/**, **/data-access-async-api/**"}}}}