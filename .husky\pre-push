#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

NEXUS_HOST="http://nexus-infra.foodtech.loc"

# Check for changes
if git diff HEAD --quiet; then
  echo "Checking $NEXUS_HOST reachability..."
  # Check if Nexus host is reachable
  if curl --output /dev/null --silent --head --fail "$NEXUS_HOST"; then
    echo "$NEXUS_HOST is reachable. Proceeding to tests run."
    yarn test:affected --nxBail
  else
    echo "$NEXUS_HOST is not reachable. Aborting."
    exit 1
  fi
else
  echo "Uncommited changes detected."
  exit 1
fi
