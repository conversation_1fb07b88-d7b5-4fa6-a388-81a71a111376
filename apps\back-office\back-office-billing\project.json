{"name": "back-office-billing", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "ft", "sourceRoot": "apps/back-office/back-office-billing/src", "tags": ["scope:back-office:billing", "type:app", "sonar:back-office"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "dependsOn": [{"dependencies": true, "target": "build"}], "options": {"outputPath": "dist/apps/billing-back-office", "index": "apps/back-office/back-office-billing/src/index.html", "main": "apps/back-office/back-office-billing/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/back-office-billing/tsconfig.app.json", "assets": ["apps/back-office/back-office-billing/src/favicon.ico", "apps/back-office/back-office-billing/src/assets"], "styles": ["apps/back-office/back-office-billing/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/back-office/back-office-billing/webpack.config.js"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "apps/back-office/back-office-billing/src/environments/config.ts", "with": "apps/back-office/back-office-billing/src/environments/config.ci.ts"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/back-office/back-office-billing/webpack.prod.config.js"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4218, "publicHost": "http://localhost:4218"}, "configurations": {"production": {"buildTarget": "back-office-billing:build:production"}, "development": {"buildTarget": "back-office-billing:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "back-office-billing:build"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/back-office/back-office-billing/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "back-office-billing:build", "port": 4218, "watch": false}, "configurations": {"development": {"buildTarget": "back-office-billing:build:development"}, "production": {"buildTarget": "back-office-billing:build:production"}}}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "billing", "hostUrl": "https://sq.foodtech.team/", "projectKey": "billing--core--back-office", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "libs/shared/**, **/data-access/**"}}}}