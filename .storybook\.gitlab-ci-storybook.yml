include:
  - project: 'devops/templates/gitlab-templates'
    file: 'js-monorepo/.gitlab-ci-downstream.yaml'

build:
  extends: .assemble
  allow_failure: true
  artifacts:
    when: always
    expire_in: 2 week
    paths:
      - dist/apps/storybook
  script:
    - yarn run storybook:build

build_image:
  extends: .build
  allow_failure: true
  needs:
    - build
  variables:
    PRODUCT_NAME: core
    CONTAINER: 'spa-core-storybook'
    APP_NAME: storybook

.base_deploy:
  allow_failure: true
  needs:
    - build_image
  variables:
    PRODUCT_NAME: core
    APP_NAME: storybook
    CONTAINER: 'spa-core-storybook'
    NAME: 'core--core--storybook'
    K8S_NAME: 'core--storybook'
    K8S_NAMESPACE: 'core'

feature:
  extends:
    - .deploy_feature
    - .base_deploy
  variables:
    HOST_PREFIX: storybook
    S3_BUCKET: ''
    CF_BUCKET: ''
  environment:
    name: 'storybook_feature'

qa:
  extends:
    - .deploy_qa
    - .base_deploy
  variables:
    S3_BUCKET: ''
    CF_BUCKET: ''
  environment:
    name: 'storybook_qa'
