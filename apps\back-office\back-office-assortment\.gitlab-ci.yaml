assortment_qualityspy:
  extends: .qualityspy-v2
  needs: [ ]
  variables:
    QS_CONFIG_PATH: apps/back-office/back-office-assortment/qualityspy.yaml
    QS_NAME: assortment--core--back-office

.base_assortment_deploy:
  needs:
    - assemble
  variables:
    PRODUCT_NAME: assortment
    APP_NAME: assortment-back-office
    CONTAINER: 'spa-assortment-back-office'
    UNCACHED_FILES: 'remoteEntry.mjs *environment.json'

assortment_feature:
  extends:
    - .base_assortment_deploy
    - .deploy_feature
  variables:
    APP_DEPLOY_URL: 'https://d3pbdyzf7elowv.cloudfront.net/apps/assortment-back-office/${CI_COMMIT_REF_SLUG}/'
  environment:
    name: 'back-office-feature/assortment'

dev_assortment:
  extends:
    - .base_assortment_deploy
    - .deploy_dev
  variables:
    APP_DEPLOY_URL: 'https://d36zhdz6hdzebq.cloudfront.net/apps/assortment-back-office/latest/'
  environment:
    name: 'back-office-dev/assortment'

qa_assortment:
  extends:
    - .base_assortment_deploy
    - .deploy_qa
  variables:
    APP_DEPLOY_URL: 'https://d3pbdyzf7elowv.cloudfront.net/apps/assortment-back-office/latest/'
  environment:
    name: 'back-office-qa/assortment'

stage_assortment:
  extends:
    - .base_assortment_deploy
    - .deploy_stage
  variables:
    APP_DEPLOY_URL: 'https://d3kd5456tkyp80.cloudfront.net/apps/assortment-back-office/latest/'
  environment:
    name: 'back-office-stage/assortment'

prod_assortment:
  extends:
    - .base_assortment_deploy
    - .deploy_prod
  variables:
    APP_DEPLOY_URL: 'https://staticv2.silpo.ua/apps/assortment-back-office/latest/'
  environment:
    name: 'back-office-prod/assortment'
