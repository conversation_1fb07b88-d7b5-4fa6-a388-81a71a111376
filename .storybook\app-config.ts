import { EnvironmentProviders, Provider } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { applicationConfig } from '@storybook/angular'
import { Environment } from '@ft/shared-environment'

export const appConfig = (...providers: (Provider | EnvironmentProviders)[]) =>
  applicationConfig({
    providers: [
      ...providers,
      provideHttpClient(),
      {
        provide: Environment,
        useValue: { appDeployUrl: '' }
      }
    ]
  })
