import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core'
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco'
import { EcomuiTableModule } from '@ft/ecom-ui2/table'
import { EcomUiCheckboxModule } from '@ft/ecom-ui2/checkbox'
import {
  CdkCell,
  CdkCellDef,
  CdkColumnDef,
  CdkHeaderCell,
  CdkHeaderCellDef,
  CdkHeaderRow,
  CdkHeaderRowDef,
  CdkRow,
  CdkRowDef,
  CdkTable
} from '@angular/cdk/table'
import { ColumnStyleWidthDirective } from '../../../directives/columnStyleWidth.directive'
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling'
import { CdkTableVirtualScrollDataSource, TableVirtualScrollModule } from 'ng-table-virtual-scroll'
import {
  AssortmentClusterSquareConfigurationReadModel,
  AssortmentClusterSquareConfigurationWriteModel,
  AssortmentReadModel,
  AssortmentUpdateWriteModel,
  ClusterReadModel,
  ClusterSquareReadModel
} from '@ft/data-access-assortment'
import { FindEktPipe } from '../../../pipes/find-ekt/find-ekt.pipe'
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { EcomuiTooltipModule } from '@ft/ecom-ui2/tooltip'
import { DropDownComponent, Selected } from '../../drop-down/drop-down.component'
import { EcomuiCdkModule } from '@ft/ecom-ui-cdk'
import { EcomUiContextMenuModule } from '@ft/ecom-ui2/context-menu'
import { EcomUiButtonModule } from '@ft/ecom-ui2/button'
import { GridSettingsService, ExtendedFieldsSettingsService } from '@ft/ecom-ui2/grid-settings'
import { RxState } from '@rx-angular/state'
import { combineLatest, of } from 'rxjs'
import { map } from 'rxjs/operators'
import { EcomuiFormFieldModule } from '@ft/ecom-ui2/form-field'
import { EcomuiSelectModule } from '@ft/ecom-ui2/select'
import {
  Cluster,
  NewState,
  ColumnsConfig,
  UpdateCluster,
  UpdateSquare,
  HeaderConfig,
  ItemTypeColumn,
  ItemEditTableKey
} from '../../../models/table-assortment.model'
import { TableEditorService } from '../../../services/table-editor.service'
import { LocalStorageService } from '@ft/shared-platform'
import { ResizeColTableDirective } from '../../../directives/resizeColTable.directive'
import { AsyncPipe } from '@angular/common'

interface State {
  total: number
  displayedColumns: string[]
  dataTable: CdkTableVirtualScrollDataSource<AssortmentReadModel>
  locoDropDown: Selected[]
  deliveryType: Selected[]
  sticky: boolean
  columnsConfig: ColumnsConfig[]
}

const header = [
  {
    color: 'rgb(246 248 253)',
    type: 'basic',
    colspan: 8,
    header: 1,
    sticky: true,
    column: 'group1',
    squares: [
      {
        colspan: 8,
        sticky: true,
        type: 'basic',
        header: 2,
        column: 'area1',
        title: '',
        color: 'rgb(246 248 253)'
      }
    ],
    title: 'Базові параметри'
  }
]

const GRID_SETTINGS_LS_KEY = 'assortmentGridSettings'
const GRID_SETTINGS_EXTRA_PARAMETERS_KEY = 'assortmentExtraParametersSettings'
const WIDTH_COLUM_CACHE = 'assortmentWidthColum'

const width = {
  productCategoryCode: 56,
  productCategory: 72,
  itemNumber: 80,
  productName: 100,
  unit: 80,
  need: 80,
  csb: 56,
  loco: 96,
  newState: 120,
  factState: 120,
  deliveryType: 120
}

@Component({
  selector: 'ft-assortment-table-assortment',
  templateUrl: './table-assortment.component.html',
  styleUrls: ['./table-assortment.component.scss', './../../main.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TranslocoDirective,
    EcomuiTableModule,
    EcomUiCheckboxModule,
    CdkCell,
    CdkCellDef,
    CdkColumnDef,
    CdkHeaderCell,
    CdkHeaderCellDef,
    CdkHeaderRow,
    CdkHeaderRowDef,
    CdkRow,
    CdkRowDef,
    CdkTable,
    ColumnStyleWidthDirective,
    CdkVirtualScrollViewport,
    TableVirtualScrollModule,
    EcomuiTooltipModule,
    FindEktPipe,
    DropDownComponent,
    ReactiveFormsModule,
    EcomuiCdkModule,
    EcomUiContextMenuModule,
    EcomUiButtonModule,
    EcomuiFormFieldModule,
    EcomuiSelectModule,
    ResizeColTableDirective,
    AsyncPipe
  ],
  standalone: true
})
export class TableAssortmentComponent extends RxState<State> implements OnInit {
  private readonly gridSettingsService = inject(GridSettingsService)
  private readonly extendedFieldsSettingsService = inject(ExtendedFieldsSettingsService)
  private localStorageService = inject(LocalStorageService)
  private translocoService = inject(TranslocoService)
  private tableEditor = inject(TableEditorService)

  @Input() set getDataTable({ data, clusters }: { data: AssortmentReadModel[]; clusters: Cluster }) {
    this.clusters = clusters
    if (!this.tableFormGroup) {
      this.createFormGroup()
      this.updateDataClusters()
    }
    if (data) {
      const updatedData = this.get('dataTable')?.data || []
      this.changeVirtualScroll([...updatedData, ...data])
      this.createIndex()
      this.createLocoControls(data)
      this.createFormArrayControl(this.updateClusters, this.get('dataTable').data)
      this.generateTableColumns()
    }
  }

  @Input() set filterCluster(clusters: Cluster) {
    if (clusters) {
      this.resetTable(clusters)
    }
  }

  @Input() totalItem!: number
  @Output() eventEndScroll = new EventEmitter<number>()
  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport
  public state$ = this.select()
  headerTable: HeaderConfig[] = header
  clusters!: Cluster
  displayedColumnsLevel1 = ['group1']
  displayedColumnsLevel2: string[] = ['area1']
  displayedColumnsLevel3: string[] = []
  displayedColumnsLevel3Original = [
    'productCategoryCode',
    'productCategory',
    'itemNumber',
    'productName',
    'unit',
    'need',
    'csb',
    'loco'
  ]
  statArray = ['factState', 'newState', 'deliveryType']
  // TODO: add type
  controls = new FormArray<FormControl<any>>([])
  tableFormGroup!: FormGroup
  headerConfig: HeaderConfig[] = header
  page = 1
  // In the future this should be select field
  levelControl = new FormControl(3)
  updateClusters: UpdateCluster[] = []
  changedCells: { value: boolean; column: string; index: number }[] = []
  time = 0
  minWidth: any = width
  changeWidth!: any
  horizonScroll!: number
  initialWidthSet = false
  borderColor = 'rgb(227 227 234)'
  gridColumns: string[] = []
  extendedColumns: string[] = []
  itemTypeColumn = ItemTypeColumn

  ngOnInit(): void {
    this.setState()
    this.generateTableHeader()
    this.updateDisplayedColumns()
    this.getNewWidth()
  }

  listenToHorizontalScroll(): void {
    if (this.viewport) {
      const viewportElement = this.viewport.elementRef.nativeElement as HTMLElement
      this.horizonScroll = viewportElement.scrollLeft
    }
  }

  onToggleSticky(sticky: boolean): void {
    this.set({ sticky })
    if (!sticky) {
      this.resetHorizontalScroll()
    }
  }

  resetHorizontalScroll(): void {
    const viewport = this.viewport.elementRef.nativeElement as HTMLElement
    viewport.scrollLeft = 0
  }

  getNewWidth(): void {
    const getWidth = this.localStorageService.getItem(WIDTH_COLUM_CACHE)
    if (getWidth) {
      this.changeWidth = JSON.parse(getWidth)
    } else {
      this.changeWidth = this.minWidth
    }
  }

  onWidthChange({ key, width }: { key: string; width: number }): void {
    this.changeWidth = { ...this.changeWidth, [key]: width }
    this.localStorageService.setItem(WIDTH_COLUM_CACHE, JSON.stringify(this.changeWidth))
  }

  updateDataClusters(): void {
    if (!this.clusters?.items) {
      this.updateClusters = []
      return
    }

    let squareCounter = 2
    this.updateClusters = this.clusters?.items.flatMap((cluster: ClusterReadModel, index: number) => {
      const squares = cluster.squares?.map((item: ClusterSquareReadModel, ind: number) => {
        return {
          ...item,
          column: `area${squareCounter++}`,
          color: cluster.color,
          colspan: this.statArray.length,
          header: 2
        }
      })
      return {
        ...cluster,
        column: `group${index + 2}`,
        squares,
        colspan: (cluster?.squares?.length || 0) * this.statArray.length,
        header: 1,
        total: squares?.length
      }
    })
  }

  createFormGroup(): void {
    this.tableFormGroup = new FormGroup({
      newState: new FormGroup({}),
      deliveryType: new FormGroup({}),
      loco: new FormArray([])
    })
  }

  createFormArrayControl(data: UpdateCluster[], tableData: AssortmentReadModel[]): void {
    if (!data) return
    const newStateGroup = this.tableFormGroup.get('newState') as FormGroup
    const deliveryTypeGroup = this.tableFormGroup.get('deliveryType') as FormGroup
    data.forEach((cluster: UpdateCluster) => {
      if (cluster.squares) {
        cluster?.squares.forEach((square: UpdateSquare) => {
          let newStateArray = newStateGroup.get(square.column) as FormArray<FormGroup>
          let deliveryTypeArray = deliveryTypeGroup.get(square.column) as FormArray

          if (!newStateArray) {
            newStateArray = new FormArray<FormGroup>([])
            newStateGroup.addControl(square.column, newStateArray)
          }

          if (!deliveryTypeArray) {
            deliveryTypeArray = new FormArray<FormControl>([])
            deliveryTypeGroup.addControl(square.column, deliveryTypeArray)
          }

          tableData.forEach((product: AssortmentReadModel) => {
            const key = `${cluster.id}_${square.id}_${product.id}`
            const setting = product.clusterSquareConfigurations?.find(
              (cfg: any) => cfg.clusterId === cluster.id && cfg.squareId === square.id
            )
            let form = new FormGroup({
              id: new FormControl(key),
              readOnlyValue: new FormControl(setting?.isOpenForSale ?? false),
              editableValue: new FormControl(setting?.isOpenForSale ?? false)
            })
            form.get('readOnlyValue')?.disable()
            newStateArray.push(form)
            const deliveryType = setting?.deliveryType
            deliveryTypeArray.push(
              new FormControl({
                id: deliveryType === 'supply' ? '1' : '2',
                name: deliveryType || 'empty'
              })
            )
          })
        })
      }
    })
  }

  createLocoControls(data: AssortmentReadModel[]): void {
    const locoArray = this.tableFormGroup.get('loco') as FormArray

    data.forEach((row: AssortmentReadModel) => {
      const locoControl = new FormControl({
        id: row.enabledForLoko ? '1' : '2',
        name: row.enabledForLoko,
        parentId: row.id
      })
      locoArray.push(locoControl)
    })
  }

  updateDisplayedColumns(): void {
    this.connect(
      'displayedColumns',
      combineLatest([
        this.gridSettingsService.select('displayedSettings'),
        of(this.displayedColumnsLevel3),
        this.extendedFieldsSettingsService.select('displayedSettings')
      ]).pipe(
        map(([gridColumns, level3Columns, extendedColumns]) => {
          this.time = 50
          const clusters = this.updateClusters
            .flatMap((item: any) => item.squares)
            .map((square: UpdateSquare) => square.column)
          this.updateColspan(extendedColumns.length, gridColumns.length)
          const orderedColumns: string[] = []
          clusters.forEach((area: string) => {
            extendedColumns.forEach((field: string) => {
              orderedColumns.push(`${area}${field}`)
            })
          })
          if (this.initialWidthSet) {
            if (
              this.gridColumns.length !== gridColumns.length ||
              this.extendedColumns.length !== extendedColumns.length
            ) {
              this.resetColumnWidth()
            }
          }
          this.initialWidthSet = true
          const filtered = orderedColumns.filter((col: string) => level3Columns.includes(col))
          this.gridColumns = gridColumns
          this.extendedColumns = extendedColumns
          return [...gridColumns, ...filtered]
        })
      )
    )
  }

  resetColumnWidth(): void {
    this.changeWidth = width
    this.localStorageService.setItem(WIDTH_COLUM_CACHE, JSON.stringify(this.changeWidth))
  }

  updateColspan(length: number, lengthSticky: number): void {
    const colspanDelta = this.statArray.length - length
    const colspanSticky = this.displayedColumnsLevel3Original.length - lengthSticky

    this.headerConfig = this.headerTable.map((el: HeaderConfig) => {
      const newColspan =
        el.header === 1
          ? el.colspan - (el.column !== 'group1' ? colspanDelta * (el.squares?.length ?? 0) : colspanSticky)
          : el.header === 2
            ? el.colspan - (el.column !== 'area1' ? colspanDelta : colspanSticky)
            : el.colspan

      return { ...el, colspan: newColspan }
    })
  }

  setState(): void {
    this.gridSettingsService.initSettingsCfg(this.displayedColumnsLevel3Original, GRID_SETTINGS_LS_KEY)
    this.extendedFieldsSettingsService.initSettingsCfg(this.statArray, GRID_SETTINGS_EXTRA_PARAMETERS_KEY)
    this.set({
      locoDropDown: [
        { id: '1', name: true },
        { id: '2', name: false }
      ],
      deliveryType: [
        { id: '1', name: 'supply' },
        { id: '2', name: 'replenish' },
        { id: '3', name: 'empty' }
      ],
      sticky: true
    })
  }

  scrollBottom(): void {
    const totalPage = Math.ceil(this.totalItem / 50)
    if (this.page < totalPage) {
      this.page++
      this.eventEndScroll.emit(this.page)
    }
  }

  onItemsChange(event: any, type: string, element: AssortmentReadModel, item?: ColumnsConfig): void {
    if (!this.tableFormGroup.dirty) return
    switch (type) {
      case ItemEditTableKey.EnabledForLoko:
        element.enabledForLoko = event?.name
        break
      case ItemEditTableKey.DeliveryType:
        if (
          element.clusterSquareConfigurations?.some(
            (csq) => csq.clusterId === item!.idCluster && csq.squareId === item?.squareId
          )
        ) {
          element.clusterSquareConfigurations?.forEach((csq) => {
            if (csq.clusterId === item!.idCluster && csq.squareId === item?.squareId) {
              csq.deliveryType = event?.name === 'empty' ? null : event.name
            }
          })
        } else {
          ;(element.clusterSquareConfigurations as AssortmentClusterSquareConfigurationWriteModel[])?.push({
            clusterId: item!.idCluster,
            deliveryType: event?.name === 'empty' ? null : event.name,
            isOpenForSale: false,
            squareId: item?.squareId!
          })
        }
        break
      case ItemEditTableKey.IsOpenForSale:
        if (
          element.clusterSquareConfigurations?.some(
            (csq) => csq.clusterId === item!.idCluster && csq.squareId === item?.squareId
          )
        ) {
          element.clusterSquareConfigurations?.forEach((csq) => {
            if (csq.clusterId === item!.idCluster && csq.squareId === item?.squareId) {
              csq.isOpenForSale = event
            }
          })
        } else {
          ;(element.clusterSquareConfigurations as AssortmentClusterSquareConfigurationWriteModel[])?.push({
            clusterId: item!.idCluster,
            deliveryType: null,
            isOpenForSale: event,
            squareId: item?.squareId!
          })
        }
        break
    }
    this.tableEditor.setItem(element as AssortmentUpdateWriteModel)
  }

  changeNewState(value: boolean, column: string, index: number): void {
    if (!this.tableFormGroup.dirty) return
    const idx = this.changedCells.findIndex((cell) => cell.column === column && cell.index === index)
    if (idx !== -1) {
      this.changedCells[idx].value = value
    } else {
      this.changedCells.push({ value, column, index })
    }
  }

  createIndex(): void {
    this.get('dataTable').data = this.get('dataTable').data.map((product: AssortmentReadModel, index: number) => ({
      ...product,
      index
    }))
  }

  changeVirtualScroll(data: AssortmentReadModel[]): void {
    const dataSource = new CdkTableVirtualScrollDataSource(data)
    this.set({ dataTable: dataSource })
  }

  trackByFn(index: number, item: AssortmentReadModel): string {
    return item.id
  }

  private generateTableHeader(): void {
    const clusters = this.updateClusters
    const headerSource = [...this.headerConfig, ...clusters]
    this.headerTable = headerSource.flatMap((header) => {
      const updateHeader = {
        ...header,
        borderColor:
          header?.type === 'basic'
            ? this.borderColor
            : header?.color
              ? this.darkenColor(header.color, 10)
              : this.borderColor
      }
      const updateSquares = header.squares?.map((square) => {
        return {
          ...square,
          borderColor:
            header?.type === 'basic'
              ? this.borderColor
              : header?.color
                ? this.darkenColor(header.color, 10)
                : this.borderColor
        }
      })
      return [updateHeader, ...(updateSquares ?? [])]
    }) as HeaderConfig[]
    this.displayedColumnsLevel1 = headerSource.map((header) => header.column)
    this.displayedColumnsLevel2 = headerSource.flatMap((header) =>
      header?.squares?.map((item: any) => item.column)
    ) as string[]

    const exclusiveStatArray = clusters
      .flatMap((cluster: UpdateCluster) => cluster?.squares?.map((item: UpdateSquare) => item.column) as string[])
      .flatMap((square: string) => this.statArray.map((item: string) => `${square}${item}`))

    this.displayedColumnsLevel3 = [...this.displayedColumnsLevel3Original, ...exclusiveStatArray]
  }

  private generateTableColumns(): void {
    const clusters = this.updateClusters
    const data = this.get('dataTable').data
    const settingsMap = new Map<string, AssortmentClusterSquareConfigurationReadModel[]>() as any
    data.forEach((product: AssortmentReadModel) => {
      product.clusterSquareConfigurations?.forEach((clusterSquare: AssortmentClusterSquareConfigurationReadModel) => {
        const key = `${clusterSquare.clusterId}_${clusterSquare.squareId}`
        if (!settingsMap.has(key)) {
          settingsMap.set(key, [])
        }
        settingsMap.get(key)!.push(clusterSquare)
      })
    })
    const columnsConfig = clusters.flatMap((cluster: UpdateCluster) => {
      const squares = cluster?.squares || []
      const lastSquareIndex = squares.length - 1
      return (
        squares?.flatMap((square: UpdateSquare, squareIndex: number) => {
          const settingKey = `${cluster.id}_${square.id}`
          const foundSettings = settingsMap.get(settingKey) || []
          return this.statArray.map((item: string, itemIndex: number, arr: string[]) => ({
            idColum: `${square.column}${item}`,
            idCluster: cluster.id,
            squareId: square.id,
            color: square.color,
            type: item,
            borderColor:
              squareIndex === lastSquareIndex && itemIndex === arr.length - 1 ? this.darkenColor(square.color, 10) : '',
            text: this.translocoService.translate(`assortmentBackOffice.settings.${item}`),
            column: square.column,
            remains: item.includes('remains') ? foundSettings.map((el: any) => el.remains) : []
          }))
        }) ?? []
      )
    })
    this.set({ columnsConfig })
  }

  get getLoco(): FormArray<FormControl> {
    return this.tableFormGroup.get('loco') as FormArray
  }

  changeForm(): void {
    const newStateGroup = this.tableFormGroup.get('newState') as FormGroup
    this.changedCells.forEach(({ column, index }: NewState) => {
      const control = (newStateGroup?.get(column) as FormArray).at(index) as FormGroup
      control?.get('readOnlyValue')?.setValue(control.get('editableValue')?.value)
    })
  }

  get newState() {
    return this.tableFormGroup.get('newState') as any
  }

  get deliveryType() {
    return this.tableFormGroup.get('deliveryType') as any
  }

  private darkenColor(hex: string, percent: number): string {
    let r = parseInt(hex.slice(1, 3), 16)
    let g = parseInt(hex.slice(3, 5), 16)
    let b = parseInt(hex.slice(5, 7), 16)

    r = Math.max(0, r - (r * percent) / 100)
    g = Math.max(0, g - (g * percent) / 100)
    b = Math.max(0, b - (b * percent) / 100)

    return `rgb(${r}, ${g}, ${b})`
  }

  private resetTable(clusters: Cluster): void {
    this.clusters = clusters
    this.updateClusters = []
    this.headerTable = header
    this.headerConfig = header
    this.displayedColumnsLevel1 = []
    this.displayedColumnsLevel2 = []
    this.displayedColumnsLevel3 = []
    this.set({ displayedColumns: [], columnsConfig: [] })
    this.createFormGroup()
    this.updateDataClusters()
    this.createFormArrayControl(this.updateClusters, this.get('dataTable').data)
    this.createLocoControls(this.get('dataTable').data)
    this.generateTableHeader()
    this.updateDisplayedColumns()
    this.generateTableColumns()
    this.resetHorizontalScroll()
  }
}
