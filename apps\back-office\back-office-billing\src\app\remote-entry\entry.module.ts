import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { RouterModule } from '@angular/router'
import { environment } from '../../environments/environment'
import { BACK_OFFICE_BILLING_ENV } from '@ft/back-office-billing-environment'
import { BillingTransactionsDataAccessModule } from '@ft/billing-transactions-data-access'
import { HTTP_INTERCEPTORS } from '@angular/common/http'
import { AuthInterceptor } from 'angular-auth-oidc-client'
import { BillingTariffDataAccessModule } from '@ft/billing-tariff-data-access'
import { BillingEmployeesDataAccessModule } from '@ft/billing-employees-data-access'
import { BillingAttributeDataAccessModule } from '@ft/billing-attribute-data-access'
import { BillingCoefficientDataAccessModule } from '@ft/billing-coefficient-data-access'

@NgModule({
  imports: [
    CommonModule,
    BillingTransactionsDataAccessModule.forRoot({ rootUrl: environment.apiUrl }),
    BillingTariffDataAccessModule.forRoot({ rootUrl: environment.apiUrl }),
    BillingEmployeesDataAccessModule.forRoot({ rootUrl: environment.apiUrl }),
    BillingAttributeDataAccessModule.forRoot({ rootUrl: environment.apiUrl }),
    BillingCoefficientDataAccessModule.forRoot({ rootUrl: environment.apiUrl }),
    RouterModule.forChild([
      {
        path: '',
        loadChildren: () => import('@ft/billing-shell').then((m) => m.BillingShellModule)
      }
    ])
  ],
  providers: [
    { provide: BACK_OFFICE_BILLING_ENV, useValue: environment },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
  ]
})
export class RemoteEntryModule {}
